package com.extracme.nevmp.schedule;

import static com.extracme.nevmp.mapper.OwnerInfoDynamicSqlSupport.ownerInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThanWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotNull;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;
import static org.mybatis.dynamic.sql.SqlBuilder.or;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.nevmp.enums.OwnerStatusEnum;
import com.extracme.nevmp.mapper.extend.OwnerInfoExtendMapper;
import com.extracme.nevmp.model.OwnerChargeInfo;
import com.extracme.nevmp.model.OwnerInfo;
import com.extracme.nevmp.model.VehicleInfo;
import com.extracme.nevmp.service.async.AsyncService;
import com.extracme.nevmp.service.owner.OwnerService;
import com.extracme.nevmp.service.qualification.OwnerQualificationService;
import com.extracme.nevmp.service.traffic.TrafficPushService;
import com.extracme.nevmp.service.vehicle.VehicleService;
import com.extracme.nevmp.utils.DateUtil;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020/12/10
 */
@RestController
@RequestMapping("schedule")
@Slf4j
public class ScheduleController {
    @Autowired
    private OwnerQualificationService ownerQualificationService;
    @Autowired
    private OwnerService ownerService;
    @Autowired
    private VehicleService vehicleService;
    @Autowired
    private OwnerInfoExtendMapper ownerInfoExtendMapper;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private TrafficPushService trafficPushService;
    @Autowired
    private TaskExecutor scheduleExecutor;

    @GetMapping("test")
    public String test() {
        Runnable runnable = () -> {
            // 模拟业务处理
            log.info(new Date() + "定时任务健康检查");
        };
        scheduleExecutor.execute(runnable);
        return "success";
    }

    @GetMapping("autoReviewOwnerQualification")
    public void autoReviewOwnerQualification() {
        log.info("任务：autoReviewOwnerQualification 被调度。 ");
        Runnable runnable = () -> {
            log.info(new Date() + "开始定时处理:各部委已审核完成数据自动审核");
            ownerQualificationService.autoReviewOwnerQualification();
            log.info(new Date() + "结束定时处理:各部委已审核完成数据自动审核");
        };
        scheduleExecutor.execute(runnable);
    }

    @GetMapping("dealExpireReconsiderQualification")
    public void dealExpireReconsiderQualification() {
        log.info("任务：dealExpireReconsiderQualification 被调度。 ");
        Runnable runnable = () -> {
            log.info(new Date() + "开始定时处理:处理过期未复核办件");
            ownerQualificationService.dealExpireReconsiderQualification();
            log.info(new Date() + "结束定时处理:处理过期未复核办件");
        };
        scheduleExecutor.execute(runnable);
    }

    @GetMapping("dealOmitQualificationApply")
    public void dealOmitQualificationApply() {
        log.info("任务：dealOmitQualificationApply 被调度。 ");
        Runnable runnable = () -> {
            log.info(new Date() + "开始定时处理:一网通办遗漏的购车资质办件任务");
            ownerQualificationService.dealOmitQualificationApply();
            log.info(new Date() + "结束定时处理:一网通办遗漏的购车资质办件任务");
        };
        scheduleExecutor.execute(runnable);
    }

    /**
     * 1小时执行一次
     */
    @GetMapping("dealOmitVehicleConfirmTask")
    public void dealOmitVehicleConfirmTask() {
        log.info("任务：dealOmitVehicleConfirmTask 被调度。 ");
        Runnable runnable = () -> {
            log.info(new Date() + "开始定时处理:一网通办遗漏的车辆信息确认办件任务");
            //理论上事项2（车辆信息确认）在用户点击提交的同时，会调用接口，自动受理
            //但是由于办件库读写存在延迟，故在用户点击提交时，无法即时读取到用户填写的结果（是确认无误，还是驳回）
            //故需要定时任务，定期的轮训待预审的办件，直到读库同步到用户结果再做处理
            ownerService.dealOmitVehicleConfirmTask();

            //2.处理车企点击提交客户确认，但是办件未创建成功的数据
            //由于车企操作数据是先生成办件，后再对充电条件确认信息进行绑定，故存在延迟，所以此处只处理已提交1个小时的数据
            Date oneHourAgo = new Date(System.currentTimeMillis() - TimeUnit.HOURS.toMillis(1));
            SelectStatementProvider selectStatement = SqlBuilder.select(ownerInfo.allColumns())
                    .from(ownerInfo)
                    .where(ownerInfo.ownerStatus, isEqualTo(OwnerStatusEnum.OWNER_CONFIRM.getOwnerStatus()))
                    .and(ownerInfo.applyNo, isNull())
                    .and(ownerInfo.uapplyNo, isNotNull())
                    .and(ownerInfo.applyToOwnerConfirmTime, isLessThanWhenPresent(oneHourAgo))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            final List<OwnerInfo> ownerInfoList = ownerInfoExtendMapper.selectMany(selectStatement);
            for(OwnerInfo recoveryOwner : ownerInfoList) {
                System.out.println("开始纠正充电条件确认信息:" + recoveryOwner.getId());
                //查询关联的车辆信息
                VehicleInfo relationVehicle = vehicleService.queryOwnerRelationVehicleInfo(recoveryOwner.getOwnerId());
                //查询关联的充电桩信息
                OwnerChargeInfo relationCharge = ownerService.getOwnerChargeInfo(recoveryOwner.getId());
                asyncService.submitOwnerApply(recoveryOwner, relationVehicle, relationCharge);
            }
            log.info(new Date() + "结束定时处理:一网通办遗漏的车辆信息确认办件任务");
        };
        scheduleExecutor.execute(runnable);
    }

    /**
     * 1天调用一次
     */
    @GetMapping("dealOmitVehicleRegistration")
    public void dealOmitVehicleRegistration(){
        log.info("任务：dealOmitVehicleRegistration 被调度。 ");
        Runnable runnable = () -> {
            log.info(new Date() + "开始定时处理:一网通办遗漏的上牌进度查询办件任务");
            //1.理论上事项3（车辆上牌进度办件）不存在待预审（未受理)的情况，一般是由于创建时请求失败造成
            //此处任务用户定期处理未受理的办件
            //TODO 此外 由于已申报（未提交）的办件无法通过接口查询，故之后需要对此处进行详细优化
            ownerService.dealOmitVehicleRegistration();

            //2.处理已审核通过，但是事项3（车辆上牌进度办件）未生成的数据
            //（由于办件审批存在延迟，故这边只处理已审批一个小时且状态未同步的数据）
            Date oneHourAgo = new Date(System.currentTimeMillis() - TimeUnit.HOURS.toMillis(1));
            SelectStatementProvider selectStatement = SqlBuilder.select(ownerInfo.allColumns())
                    .from(ownerInfo)
                    .where(ownerInfo.uapplyNo, isNotNull())
                    .and(ownerInfo.ownerStatus, isEqualTo(OwnerStatusEnum.APPROVE.getOwnerStatus()))
                    .and(ownerInfo.applyNo, isNotNull())
                    .and(ownerInfo.vehicleApplyNo,isNull())
                    .and(ownerInfo.chargeAuditTime,isLessThanWhenPresent(oneHourAgo))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            final List<OwnerInfo> ownerInfoList = ownerInfoExtendMapper.selectMany(selectStatement);

            for(OwnerInfo recoveryOwner : ownerInfoList){
                System.out.println("开始纠正充电条件确认信息:" + recoveryOwner.getId());
                asyncService.approveOwnerApply(recoveryOwner.getApplyNo(), recoveryOwner);
            }
            log.info(new Date() + "结束定时处理:一网通办遗漏的上牌进度查询办件任务");
        };
        scheduleExecutor.execute(runnable);
    }



    @GetMapping("queryOuterDriverHasNewEnergy")
    public void queryOuterDriverHasNewEnergy() {
        log.info("任务：queryOuterDriverHasNewEnergy 被调度。 ");
        Runnable runnable = () -> {
            log.info("开始查询外地驾照名下有无新能源车");
            ownerQualificationService.queryOuterDriverHasNewEnergy();
            log.info("结束查询外地驾照名下有无新能源车");
        };
        scheduleExecutor.execute(runnable);
    }


    @GetMapping("dealPendingQualificationReview")
    public void dealPendingQualificationReview() {
        log.info("任务：dealPendingQualificationReview 被调度。 ");
        Runnable runnable = () -> {
            log.info(new Date() + "开始定时处理:各部委发送失败请求进行重试");
            ownerQualificationService.dealPendingQualificationReview();
            log.info(new Date() + "结束定时处理:各部委发送失败请求进行重试");
        };
        scheduleExecutor.execute(runnable);
    }



    @GetMapping("syncVehicleRegistrationProgress")
    public void syncVehicleRegistrationProgress() {
        log.info("任务：syncVehicleRegistrationProgress 被调度。 ");
        Runnable runnable = () -> {
            log.info(new Date() + "开始定时处理:用户上牌进度校验");

            Long lastId = 0L;
            for (int i = 0; i < 1000; i++) {
                //查询所有审核通过且未同步牌照的数据（且有办件3的,审核通过的，有效的,且最近100天的-预留10天）

                SelectStatementProvider selectStatement = SqlBuilder.select(ownerInfo.id, ownerInfo.vin, ownerInfo.vehicleApplyNo)
                        .from(ownerInfo)
                        .where()
                        .and(ownerInfo.vehicleApplyNo, isNotNull())
                        .and(ownerInfo.vehicleApplyNo, isNotEqualTo(""))
                        .and(ownerInfo.ownerStatus, isEqualTo(OwnerStatusEnum.APPROVE.getOwnerStatus()))
                        .and(ownerInfo.vehicleNo, isNull(), or(ownerInfo.vehicleNo, isEqualTo("")))
                        .and(ownerInfo.stakeId, isNull(), or(ownerInfo.stakeId, isEqualTo(0)))
                        .and(ownerInfo.vin, isNotNull())
                        .and(ownerInfo.vin, isNotEqualTo(""))
                        .and(ownerInfo.flag, isEqualTo(1))
                        .and(ownerInfo.disable, isEqualTo(1))
                        .and(ownerInfo.id, isGreaterThanWhenPresent(lastId))
//                        .orderBy(ownerInfo.id)
                        .limit(100)
                        .build()
                        .render(RenderingStrategies.MYBATIS3);
                final List<OwnerInfo> ownerInfoList = ownerInfoExtendMapper.selectMany(selectStatement);
                if (ownerInfoList.size() == 0) {
                    break;
                }
                for (OwnerInfo checkOwnerInfo : ownerInfoList) {
                    asyncService.syncVehicleRegistrationProgress(checkOwnerInfo);
                    lastId = checkOwnerInfo.getId();
                }
            }
            log.info(new Date() + "结束定时处理:用户上牌进度校验");
        };
        scheduleExecutor.execute(runnable);
    }



    @GetMapping("trafficPushData")
    public void trafficPushData() {
        log.info("任务：trafficPushData 被调度。 ");
        Runnable runnable = () -> {
            log.info(new Date() + "开始定时处理：交委推送数据");
            trafficPushService.pushData();
            log.info(new Date() + "结束定时处理：交委推送数据");
        };
        scheduleExecutor.execute(runnable);
    }

    @GetMapping("trafficCommitteePush")
    public void trafficCommitteePush() {
        log.info("任务：trafficCommitteePush 被调度。 ");
        Runnable runnable = () -> {
            log.info(new Date() + "开始定时处理：交委推送数据查非营运车额度");
            trafficPushService.trafficCommitteePush();
            log.info(new Date() + "结束定时处理：交委推送数据查非营运车额度");


            log.info(new Date() + "开始定时处理：交委推送数据查以旧换新专用额度");
            trafficPushService.trafficCommitteeExchangePush();
            log.info(new Date() + "结束定时处理：交委推送数据查以旧换新专用额度");

        };
        scheduleExecutor.execute(runnable);
    }

    @GetMapping("trafficCommitteePull")
    public void trafficCommitteePull() {
        log.info("任务：trafficCommitteePull 被调度。 ");
        Runnable runnable = () -> {

            //拉取前一天的额度数据
            String dateStr = DateUtil.format(DateUtil.addDays(new Date(), -1), DateUtil.DATE_TYPE3);

            log.info(new Date() + "开始定时处理：交委拉取数据查非营运车额度");
            trafficPushService.trafficCommitteePull();
            trafficPushService.trafficCommitteePull(dateStr);
            log.info(new Date() + "结束定时处理：交委拉取数据查非营运车额度");


            log.info(new Date() + "开始定时处理：交委拉取数据查以旧换新额度");
            trafficPushService.trafficCommitteeExchangePull();
            trafficPushService.trafficCommitteeExchangePull(dateStr);
            log.info(new Date() + "结束定时处理：交委拉取数据查以旧换新额度");
        };
        scheduleExecutor.execute(runnable);
    }

    @GetMapping("trafficCommitteePullWithDate/{date}")
    public void trafficCommitteePullWithDate(@PathVariable("date") String date) {
        Runnable runnable = () -> {
            trafficPushService.trafficCommitteePull(date);
        };
        scheduleExecutor.execute(runnable);
    }

    @GetMapping("autoProcessOwnerReview")
    @ApiOperation(value = "自动审核充电条件确认信息", httpMethod = "GET")
    public void autoProcessOwnerReview(){
        Runnable runnable = () -> {
            log.info(new Date() + "开始定时处理：自动审核充电条件确认信息");
            ownerService.autoProcessOwnerReview();
            log.info(new Date() + "结束定时处理：自动审核充电条件确认信息");
        };
        scheduleExecutor.execute(runnable);
    }

    @GetMapping("autoSyncVehicleModelFirstRegTime")
    @ApiOperation(value = "自动同步车型首张确认凭证时间", httpMethod = "GET")
    public void autoSyncVehicleModelFirstRegTime(){
        Runnable runnable = () -> {
            log.info(new Date() + "开始定时处理：自动同步车型首张确认凭证时间");
            ownerService.autoSyncVehicleModelFirstRegTime();
            log.info(new Date() + "结束定时处理：自动同步车型首张确认凭证时间");
        };
        scheduleExecutor.execute(runnable);


    }
}
